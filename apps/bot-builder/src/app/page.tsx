import { Button } from '@b6-ai-ui/ui';
import { Input } from '@b6-ai-ui/ui';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@b6-ai-ui/ui';

export default function Index() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto p-8">
        <div className="space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              Welcome to Bot Builder 🤖
            </h1>
            <p className="text-muted-foreground">
              Build amazing bots with our centralized UI components
            </p>
          </div>

          {/* Theme Toggle Section */}
          <Card>
            <CardHeader>
              <CardTitle>Theme Demo</CardTitle>
              <CardDescription>
                Toggle between light and dark themes to see the centralized
                theming in action
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={() =>
                    document.documentElement.classList.remove('dark')
                  }
                  variant="outline"
                >
                  Light Theme
                </Button>
                <Button
                  onClick={() => document.documentElement.classList.add('dark')}
                  variant="outline"
                >
                  Dark Theme
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Bot Builder Specific Components */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Bot Configuration</CardTitle>
                <CardDescription>Configure your bot settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input placeholder="Bot name" />
                <Input placeholder="Bot description" />
                <div className="flex gap-2">
                  <Button size="sm">Save</Button>
                  <Button variant="outline" size="sm">
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common bot builder actions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" variant="secondary">
                  Create New Bot
                </Button>
                <Button className="w-full" variant="outline">
                  Import Bot
                </Button>
                <Button className="w-full" variant="ghost">
                  View Templates
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
