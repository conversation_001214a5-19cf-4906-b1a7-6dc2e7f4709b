{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["development"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/ui/*": ["libs/ui/src/components/ui/*"], "@/lib/*": ["libs/ui/src/lib/*"], "@b6-ai-ui/ui": ["libs/ui/src/index.ts"], "@b6-ai-ui/ui/*": ["libs/ui/src/*"]}}}